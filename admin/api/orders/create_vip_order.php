<?php
/**
 * 创建VIP升级订单API（使用新生易支付）
 * API路径: /admin/api/orders/create_vip_order.php
 * 请求方式: POST
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 简单的响应函数
function apiSuccess($data = null, $message = 'success') {
    echo json_encode([
        'code' => 0,
        'message' => $message,
        'data' => $data
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

function apiError($code, $message) {
    http_response_code(400);
    echo json_encode([
        'code' => $code,
        'message' => $message,
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 测试endpoint
if (isset($_GET['test'])) {
    apiSuccess(['status' => 'API正常工作', 'time' => date('Y-m-d H:i:s')]);
}

// VIP订单创建API正式版本

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    apiError(405, '仅支持POST请求');
}

// 获取JSON输入
$input = file_get_contents('php://input');
$data = json_decode($input, true);

if (!$data) {
    apiError(400, '无效的JSON数据');
}

// 验证参数
if (!isset($data['openid']) || empty($data['openid'])) {
    apiError(400, '缺少必要参数: openid');
}

// 引入数据库连接
require_once __DIR__ . '/../config.php';

// 简化的用户验证
function validateUserToken() {
    $token = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    $token = str_replace('Bearer ', '', $token);
    
    if (empty($token)) {
        return false;
    }
    
    try {
        $conn = get_connection('main');
        $stmt = $conn->prepare("SELECT u.id, u.name, u.phone, u.is_vip, u.is_vip_paid, u.wechat_openid FROM app_users u JOIN auth_tokens t ON u.id = t.user_id WHERE t.token = ? AND t.expires_at > NOW()");
        $stmt->bind_param("s", $token);
        $stmt->execute();
        $result = $stmt->get_result();
        $user = $result->fetch_assoc();
        $conn->close();
        
        return $user;
    } catch (Exception $e) {
        error_log("Token validation error: " . $e->getMessage());
        return false;
    }
}

// 验证用户登录状态
$user = validateUserToken();
if (!$user) {
    apiError(401, '未登录或登录已过期');
}

$userId = $user['id'];
$openid = $data['openid'];
$referrerId = isset($data['referrer_id']) ? intval($data['referrer_id']) : 0;

try {
    // 连接数据库
    $conn = get_connection('main');
    
    // 生成订单号
    $orderNo = 'VIP' . date('YmdHis') . rand(1000, 9999);
    $vipAmount = 1888.00;
    
    // 创建订单记录
    $insertOrderSql = "INSERT INTO orders (
        order_no, user_id, product_type, product_name, total_amount, actual_amount,
        payment_status, status, referrer_id, 
        receiver_name, receiver_phone, receiver_province, receiver_city, receiver_district, receiver_address,
        created_at, updated_at
    ) VALUES (?, ?, 'vip', 'VIP会员升级', ?, ?, 'pending', 0, ?, '系统用户', '13800000000', '系统', '系统', '系统', '系统地址', NOW(), NOW())";
    
    $insertOrderStmt = $conn->prepare($insertOrderSql);
    $insertOrderStmt->bind_param("siddi", $orderNo, $userId, $vipAmount, $vipAmount, $referrerId);
    
    if ($insertOrderStmt->execute()) {
        $orderId = $conn->insert_id;
        $conn->close();

        // 模拟微信支付参数（实际项目中应该调用微信支付API获取）
        $paymentParams = [
            'appId' => 'your_app_id',
            'timeStamp' => (string)time(),
            'nonceStr' => md5(uniqid()),
            'package' => 'prepay_id=your_prepay_id',
            'signType' => 'MD5',
            'paySign' => 'your_pay_sign'
        ];

        // 返回成功响应
        apiSuccess([
            'order_no' => $orderNo,
            'order_id' => $orderId,
            'amount' => $vipAmount,
            'payment_params' => $paymentParams,
            'message' => '订单创建成功'
        ]);
    } else {
        $conn->close();
        apiError(500, '创建订单失败: ' . $insertOrderStmt->error);
    }
    
} catch (Exception $e) {
    if (isset($conn)) $conn->close();
    apiError(500, '系统错误: ' . $e->getMessage());
}
?> 