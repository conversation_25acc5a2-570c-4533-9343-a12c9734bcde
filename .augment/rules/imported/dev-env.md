---
type: "agent_requested"
---

# Tapp 项目开发环境规则

## 目录与开发环境
- 本项目所有开发均在服务器 `/www/wwwroot/pay.itapgo.com/Tapp` 目录下直接进行。
- 禁止在根目录新建文件夹，所有新开发应在 `app-vue/` 或 `admin/` 下进行。
- 生产环境域名：https://pay.itapgo.com
- 手机端访问路径：https://pay.itapgo.com/app/#/
- 手机端登录入口：https://pay.itapgo.com/app/#/login
- 管理后台访问路径：https://pay.itapgo.com/admin/#/
- 管理后台登录入口：https://pay.itapgo.com/admin/#/login

## 构建与部署
- 管理后台构建脚本：`admin/.build.sh`，构建后部署到 `/www/wwwroot/pay.itapgo.com/admin`
- 手机端构建脚本：`app-vue/.build.sh`，构建后部署到 `/www/wwwroot/pay.itapgo.com/app`
- 构建时请进入对应目录后执行 `./build.sh`，不要在根目录或其他目录执行。

## 服务器与运行环境
- 服务器已配置 Nginx + PHP 8.1 + MySQL 5.7 + SSL
- Nginx 配置文件路径：`/www/server/panel/vhost/nginx/pay.itapgo.com.conf`
- 数据库配置保存在 `admin/.env` 文件中，涉及主数据库（ddg.app）、支付系统数据库（b.tapgo.cn）、净水器系统数据库（jzq_water_plat）
- 所有数据库连接、敏感信息仅允许配置在 `admin/.env`，严禁泄露

## 代码与API规范
- 手机端（app-vue/）严禁直连数据库，所有数据交互必须通过后台API
- 推荐API开发方式为 Laravel RESTful API，所有新API应在 `admin/routes/api.php` 配置
- V1版本API路径：手机端VI版本API路径/www/wwwroot/pay.itapgo.com/Tapp/admin/app/Http/Controllers/Mobile/Api/V1
管理后台V1版本API路径/www/wwwroot/pay.itapgo.com/Tapp/admin/app/Http/Controllers/Admin/Api/V1
- 原生PHP API位于 `admin/api/`，逐步迁移至Laravel API
- 前端API调用统一通过 `app-vue/src/utils/request.js` 封装，API模块放在 `app-vue/src/api/`
- 路径引用请使用相对路径，不要使用绝对路径如 `/www/wwwroot/pay.itapgo.com/Tapp`

## 其他注意事项
- 管理后台和手机端打包部署后需及时验证功能，确保无误
- 禁止在生产环境暴露调试信息
- 任何变更需同步完善 `README.md` 和相关开发文档，保持文档与代码一致
