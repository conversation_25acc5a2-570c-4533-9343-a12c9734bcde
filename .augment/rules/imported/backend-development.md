---
type: "always_apply"
---

# 点点够后端开发规范

## 技术栈
- 后端框架: Laravel
- PHP版本: 8.1
- 数据库: MySQL

## 后端目录结构
- [admin/api/](mdc:admin/api) - 原生PHP实现的API
- [admin/app/](mdc:admin/app) - Laravel应用核心代码
- [admin/routes/](mdc:admin/routes) - Laravel路由定义
- [admin/config/](mdc:admin/config) - 配置文件

## API开发规范
- 优先使用Laravel RESTful API
- 原生PHP API按功能模块放在admin/api/对应目录下
- 所有API必须验证用户权限和身份

## 数据库规范
- 数据库配置保存在admin/.env中
- 主要数据库: ddg.app
- 支付系统数据库: b.tapgo.cn
- 净水器系统数据库: jzq_water_plat

## 构建与部署
- 构建命令: 在admin目录下执行./.build.sh

## 注意事项
- API必须按模块组织，保持代码结构清晰
- 敏感数据不得在前端暴露
- 密码等敏感信息必须加密存储