---
type: "always_apply"
---

# 点点够前端开发规范

## 技术栈
- 手机端: Vue3 + Vite + Pinia
- 管理后台: Laravel + Vue3 + Vant Icons

## 手机端目录结构
- [app-vue/src/api/](mdc:app-vue/src/api) - API接口封装
- [app-vue/src/views/](mdc:app-vue/src/views) - 页面视图 
- [app-vue/src/components/](mdc:app-vue/src/components) - 组件
- [app-vue/src/stores/](mdc:app-vue/src/stores) - Pinia状态管理

## 开发规范
- 组件放在app-vue/src/components/目录下
- 页面视图放在app-vue/src/views/目录下
- API封装放在app-vue/src/api/目录下
- 所有请求通过request.js发起

## 构建与部署
- 构建命令: 在app-vue目录下执行./.build.sh

## 注意事项
- 严禁前端直连数据库，必须通过后台API获取数据