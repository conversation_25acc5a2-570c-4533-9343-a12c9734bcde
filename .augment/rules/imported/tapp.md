---
type: "always_apply"
---

- nginx配置文件：/www/server/panel/vhost/nginx/pay.itapgo.com.conf
- 手机前端：https://pay.itapgo.com/app/#/
- 管理后台：https://pay.itapgo.com/admin/#/
- 用户希望根据构建脚本来修改nginx和站点配置



# Your rule content
我的app客户端是中国大陆用户在用的；
•app-vue/ 是手机端，使用 vue+vite (使用华为云CDN快速开发)；
•admin/ 是管理后台，使用 Laravel + Vue3 + Vant Icons；
•手机端app-vue不直连数据库，都是通过admin目录下的Laravel风格接口来获取数据（之前开发的时候用到了大量的PHP原生API，逐步全量进行迁移）；
•项目运行在配置了 Nginx + PHP8.1 + SSL 的服务器上（域名：https://pay.itapgo.com）；
•数据库配置保存在 admin/.env 中；
•nginx配置文件：/www/server/panel/vhost/nginx/pay.itapgo.com.conf
•管理后台前端打包（构建部署）命令脚本在admin目录下的.build.sh
•手机端打包（构建部署）命令脚本在app-vue目录下的.build.sh
•改动app手机端时，都要考虑与数据库、后台打通



# 项目结构说明
我的项目是点点够应用系统，包含以下主要目录：
- admin/ 是管理后台，使用 Laravel + Vue3 + Vant Icons
- app-vue/ 是手机端，使用 Vue3 + Vite（是主要开发目录，替代了旧的app/目录）
- 手机端不直连数据库，都是通过admin/api和admin/public/api来获取数据

# 目录结构精确说明
Tapp/
├── admin/                  # 管理后台 (Laravel + Vue3)
│   ├── api/                # 原生PHP实现的API（给手机端调用的）
│   │   ├── app/            # 商城应用API
│   │   ├── device/         # 设备相关API
│   │   ├── installation/   # 设备安装API
│   │   ├── merchant/       # 商户管理API
│   │   ├── salesman/       # 业务员API
│   │   ├── user/           # 用户API
│   │   ├── wechat/         # 微信相关API
│   │   ├── functions/      # API功能函数
│   │   ├── config.php      # API配置文件
│   │   └── index.php       # API入口文件
│   ├── app/                # Laravel应用核心代码
│   ├── routes/             # Laravel路由定义
│   │   ├── api.php         # Laravel API路由（RESTful API）
│   │   └── web.php         # Web路由
│   ├── public/             # 公共访问目录
│   └── .env                # 环境配置
├── app-vue/                # 手机端Vue应用
│   ├── src/                # 源代码目录
│   │   ├── api/            # API接口封装
│   │   ├── assets/         # 静态资源
│   │   ├── components/     # 组件
│   │   ├── router/         # 路由配置
│   │   ├── stores/         # Pinia状态管理
│   │   ├── utils/          # 工具函数
│   │   ├── views/          # 页面视图
│   │   └── main.js         # 入口文件
│   ├── dist/               # 构建输出目录
│   └── index.html          # HTML入口
└── README.md               # 项目说明

# API路径说明
系统包含两种API实现方式：
1. Laravel RESTful API: 位于admin/routes/api.php，路径为/api/开头
2. 原生PHP API: 位于admin/api/目录，通过URL直接访问，如/admin/api/user/login.php

# 前端API调用说明
app-vue中的API调用统一通过src/utils/request.js封装，基础URL为https://pay.itapgo.com/Tapp/admin/api
在app-vue/src/api/目录下按功能模块组织API调用，如user.js、device.js、merchant.js等

# 数据库链接
所有数据库链接配置都保存在admin/.env文件中，包括：
- ddg.app：主数据库
- b.tapgo.cn：支付系统数据库
- jzq_water_plat：净水器系统数据库

# 常见错误纠正
- 不要在根目录下创建新文件夹，新开发都应在app-vue/或admin/下进行
- API路径应当使用/admin/api/user/login.php这样的格式，不要使用/api/user/login
- 前端API模块应放在app-vue/src/api/目录下，并通过request.js调用
- 所有前端页面应该放在app-vue/src/views/目录下
- 不要使用绝对路径/www/wwwroot/pay.itapgo.com/Tapp，应使用相对路径

# 开发规范与注意事项

## 一、项目定位与用户
- 本系统主要服务于中国大陆用户，所有功能、交互、合规性均需符合中国大陆相关政策与用户习惯。
- 手机端（app-vue/）为主要用户入口，管理后台（admin/）为运营与管理人员使用。

## 二、目录结构与开发约定
### 1. 目录结构精确说明
- admin/：管理后台，基于 Laravel + Vue3 + Vant Icons
  - api/：原生PHP实现的API，供手机端调用，按业务模块细分（如user、device、merchant等）
  - routes/：Laravel风格RESTful API路由（api.php、web.php）
  - public/：公共静态资源目录
  - .env：数据库及环境配置
  - .build.sh：管理后台构建部署脚本
- app-vue/：手机端Vue3应用
  - src/api/：前端API接口封装，所有接口调用均通过此目录下模块，并统一通过src/utils/request.js发起
  - src/views/：所有页面视图，按业务模块细分
  - .build.sh：手机端构建部署脚本
- 数据库配置：全部保存在admin/.env，涉及主数据库、支付系统、净水器系统等

### 2. API调用与数据流
- 手机端严禁直连数据库，所有数据交互必须通过后台API（admin/api/ 或 Laravel RESTful API）。
- 推荐API路径格式：/Tapp/admin/api/user/login.php（原生PHP），或/Tapp/admin/api.php（RESTful）。
- 前端API调用统一通过app-vue/src/utils/request.js进行封装，便于统一管理和拦截处理。

### 3. 开发与部署
- 管理后台打包命令：进入admin/目录，执行./build.sh
- 手机端打包命令：进入app-vue/目录，执行./build.sh
- Nginx配置：详见/www/server/panel/vhost/nginx/pay.itapgo.com.conf
- 生产环境域名：https://pay.itapgo.com

### 4. 常见错误与规范
- 禁止在根目录新建文件夹，所有新开发应在app-vue/或admin/下进行。
- API路径必须使用/admin/api/xxx.php格式，不要用/api/xxx。
- 前端API模块必须放在app-vue/src/api/，页面放在app-vue/src/views/。
- 路径引用请使用相对路径，不要使用绝对路径如/www/wwwroot/pay.itapgo.com/Tapp。

### 5. 数据库与安全
- 数据库连接信息仅允许配置在admin/.env，严禁泄露。
- 手机端所有敏感操作必须通过后台接口校验权限和身份。

## 三、开发流程建议
1. 新功能开发：优先在app-vue/和admin/下按模块开发，API优先采用Laravel RESTful风格，逐步淘汰原生PHP API。
2. 接口联调：前端开发需与后台API保持同步，接口变更需及时同步app-vue/src/api/模块。
3. 文档维护：每次开发或变更，务必同步完善README.md和相关开发文档，保持文档与代码一致。

## 四、参考文档
- docs/点点够App项目综合文档.md
- docs/API文档.md
- docs/数据库说明文档.md
- docs/Nginx配置说明.md