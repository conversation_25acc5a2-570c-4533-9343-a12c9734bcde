---
type: "agent_requested"
---

# API迁移策略与架构指南

## API系统架构

系统目前存在两种API实现方式：

1. **原生PHP API**: 
   - 位于[admin/api/](mdc:admin/api)目录
   - 通过直接URL访问，如`/admin/api/user/login.php`
   - 正在逐步迁移至Laravel API

2. **Laravel RESTful API**: 
   - 位于[admin/routes/api.php](mdc:admin/routes/api.php)
   - 控制器位于[admin/app/Http/Controllers/Api](mdc:admin/app/Http/Controllers/Api)
   - 访问路径为`/api/`开头
   - 推荐优先使用这种方式开发新接口

## API迁移策略

为保障系统稳定性并逐步现代化，我们采用以下API迁移策略：

1. **阶段式迁移**：
   - 所有新功能优先使用Laravel RESTful API开发
   - 现有功能逐步迁移，确保旧API路径通过重定向保持兼容

2. **重定向机制**：
   - 使用Laravel中间件处理旧API路径到新API的内部重定向
   - 通过Nginx配置实现外部重定向

3. **版本控制**：
   - 新API采用版本前缀，如`/api/v1/user/login`
   - 逐步推动前端调用从旧路径迁移到新路径

## 前端API调用更新

- 前端API封装在[app-vue/src/api/](mdc:app-vue/src/api)目录中
- 更新API调用时，保持相同的函数名和参数，仅修改内部路径
- 示例：
  ```js
  // 旧调用
  login(data) {
    return request({
      url: '/admin/api/user/login.php',
      method: 'post',
      data
    })
  }
  
  // 新调用
  login(data) {
    return request({
      url: '/api/v1/user/login',
      method: 'post',
      data
    })
  }
  ```

## 迁移检查清单

迁移API时，请检查以下项目：

1. 确认原生API的所有参数和返回结构
2. 在Laravel控制器中复制完全相同的业务逻辑
3. 添加合适的中间件进行权限验证
4. 更新API文档
5. 在前端API模块中更新调用路径
6. 添加路径重定向规则保证兼容性

## 迁移优先级

1. 用户认证相关API：登录、注册、身份验证
2. 核心业务流程API：订单、支付、设备管理
3. 辅助功能API：通知、日志、统计

## 技术规范

- 使用Laravel资源控制器处理RESTful资源
- 统一响应格式，使用JSON格式返回数据
- 使用Laravel验证器进行参数验证
- 实现API限流防止滥用
