---
type: "always_apply"
---

# 点点够API结构和调用规范

## API类型

系统包含两种API实现方式：

1. **Laravel RESTful API**: 
   - 位于[admin/routes/api.php](mdc:admin/routes/api.php)
   - 访问路径为`/api/`开头
   - 推荐优先使用这种方式开发新接口

2. **原生PHP API**: 
   - 位于[admin/api/](mdc:admin/api)目录
   - 通过URL直接访问，如`/admin/api/user/login.php`
   - 正在逐步迁移至Laravel API

## 前端API调用

- 前端API封装在[app-vue/src/api/](mdc:app-vue/src/api)目录中
- 所有API请求通过[app-vue/src/utils/request.js](mdc:app-vue/src/utils/request.js)统一处理
- 基础URL为`/admin/api`

## API开发规范

- 手机端不能直连数据库，所有数据交互必须通过后台API
- API路径格式：
  - 原生PHP API: `/admin/api/模块/文件.php`
  - Laravel API: `/api/路由名称`
- 新API开发优先使用Laravel RESTful风格
- API接口变更需同步更新前端API封装模块