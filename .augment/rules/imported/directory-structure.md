---
type: "always_apply"
---

# 点点够应用系统目录结构

## 核心目录说明

### 前端应用
- [app-vue/](mdc:app-vue) - 手机端Vue3应用（主要开发目录）
  - [app-vue/src/](mdc:app-vue/src) - Vue3源代码
  - [app-vue/src/api/](mdc:app-vue/src/api) - 前端API封装模块
  - [app-vue/src/views/](mdc:app-vue/src/views) - 前端页面视图
  - [app-vue/src/components/](mdc:app-vue/src/components) - 前端组件
  - [app-vue/src/router/](mdc:app-vue/src/router) - 前端路由配置
  - [app-vue/src/stores/](mdc:app-vue/src/stores) - Pinia状态管理

### 后端应用
- [admin/](mdc:admin) - 管理后台（Laravel + Vue3）
  - [admin/api/](mdc:admin/api) - 原生PHP实现的API
  - [admin/app/](mdc:admin/app) - Laravel应用核心代码
  - [admin/routes/](mdc:admin/routes) - Laravel路由定义
  - [admin/public/](mdc:admin/public) - 公共访问目录
  
## 开发与部署
- 管理后台打包：admin目录下的.build.sh
- 手机端打包：app-vue目录下的.build.sh
- 生产环境域名：https://pay.itapgo.com

## 重要规范
- 所有新开发应在app-vue/或admin/下进行
- 前端API模块必须放在app-vue/src/api/
- 前端页面必须放在app-vue/src/views/
- 手机端严禁直连数据库，必须通过后台API