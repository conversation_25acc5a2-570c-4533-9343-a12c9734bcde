<template>
  <div class="landing-page">
    <!-- 头部导航 -->
    <van-nav-bar
      title="VIP会员邀请"
      left-arrow
      @click-left="goBack"
      fixed
      placeholder
    />

    <div class="page-content">
      <!-- 推荐人信息卡片 -->
      <div class="referrer-card" v-if="referrerInfo.id">
        <div class="referrer-info">
          <van-image
            round
            width="60"
            height="60"
            :src="referrerInfo.avatar"
            :error-content="'头像'"
          />
          <div class="referrer-details">
            <div class="referrer-name">{{ referrerInfo.name }}</div>
            <div class="referrer-title">VIP会员</div>
            <div class="invite-text">邀请您加入VIP会员</div>
          </div>
        </div>
      </div>

      <!-- VIP介绍卡片 -->
      <div class="intro-card">
        <div class="intro-header">
          <van-icon name="crown-o" color="#ff9500" size="24" />
          <div class="intro-title">VIP会员特权</div>
        </div>
        <div class="privilege-grid">
          <div class="privilege-item">
            <van-icon name="gold-coin-o" color="#ff9500" />
            <div class="privilege-title">分红收益</div>
            <div class="privilege-desc">享受月度分红池分红</div>
          </div>
          <div class="privilege-item">
            <van-icon name="friends-o" color="#1989fa" />
            <div class="privilege-title">团队奖励</div>
            <div class="privilege-desc">推荐他人获得奖励</div>
          </div>
          <div class="privilege-item">
            <van-icon name="diamond-o" color="#ee0a24" />
            <div class="privilege-title">专属服务</div>
            <div class="privilege-desc">VIP专属客服服务</div>
          </div>
          <div class="privilege-item">
            <van-icon name="medal-o" color="#ff9500" />
            <div class="privilege-title">尊贵身份</div>
            <div class="privilege-desc">VIP身份标识</div>
          </div>
        </div>
      </div>

      <!-- VIP价格卡片 -->
      <div class="price-card">
        <div class="price-header">
          <div class="price-title">VIP会员价格</div>
          <div class="price-subtitle">一次付费，终身享受</div>
        </div>
        <div class="price-content">
          <div class="price-main">
            <span class="price-symbol">¥</span>
            <span class="price-value">1888</span>
          </div>
          <div class="price-desc">VIP会员终身特权 + 自用优惠权益</div>
          <div class="price-benefits">
            <div class="benefit-item">
              <van-icon name="success" color="#52c41a" />
              <span>免120元安装费</span>
            </div>
            <div class="benefit-item">
              <van-icon name="success" color="#52c41a" />
              <span>终身VIP权益</span>
            </div>
            <div class="benefit-item">
              <van-icon name="success" color="#52c41a" />
              <span>月度分红收益</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 绑定状态提示 -->
      <div class="bind-status-card" v-if="bindStatus !== 'pending'">
        <div class="status-content">
          <van-icon 
            :name="getStatusIcon()" 
            :color="getStatusColor()" 
            size="20" 
          />
          <span class="status-text">{{ getStatusText() }}</span>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-section">
        <template v-if="!userStore.isLoggedIn">
          <!-- 未登录状态 -->
          <div class="login-prompt">
            <div class="prompt-text">请先登录后加入VIP会员</div>
            <div class="login-buttons">
              <van-button 
                type="primary" 
                size="large" 
                block
                @click="goToLogin"
              >
                立即登录
              </van-button>
              <van-button 
                type="default" 
                size="large" 
                block
                @click="goToRegister"
              >
                注册新用户
              </van-button>
            </div>
          </div>
        </template>
        <template v-else>
          <!-- 已登录状态 -->
          <template v-if="bindStatus === 'pending'">
            <van-button 
              type="primary" 
              size="large" 
              block
              @click="bindReferrerRelation"
              :loading="binding"
            >
              {{ binding ? '绑定中...' : '接受邀请' }}
            </van-button>
          </template>
          <template v-else-if="bindStatus === 'success'">
            <van-button 
              type="success" 
              size="large" 
              block
              @click="goToVipUpgrade"
            >
              立即升级VIP
            </van-button>
          </template>
          <template v-else-if="bindStatus === 'already_bound'">
            <van-button 
              type="success" 
              size="large" 
              block
              @click="goToVipUpgrade"
            >
              立即升级VIP
            </van-button>
          </template>
          <template v-else-if="bindStatus === 'conflict'">
            <van-button 
              type="primary" 
              size="large" 
              block
              @click="goToVipUpgrade"
            >
              立即升级VIP
            </van-button>
          </template>
          <template v-else>
            <van-button 
              type="primary" 
              size="large" 
              block
              @click="goToVipUpgrade"
            >
              立即升级VIP
            </van-button>
          </template>
        </template>
      </div>

      <!-- 温馨提示 -->
      <div class="tips-card">
        <div class="tips-title">
          <van-icon name="info-o" />
          <span>温馨提示</span>
        </div>
        <div class="tips-content">
          <div class="tip-item">• 通过此链接升级VIP，将自动绑定推荐关系</div>
          <div class="tip-item">• VIP会员享受终身特权，无需续费</div>
          <div class="tip-item">• 升级后即可开始享受分红收益</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Toast } from 'vant'
import { useUserStore } from '@/stores/user'
import { getReferrerInfo, bindReferrer, createVipOrder } from '@/api/vipRecruit'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const referrerInfo = ref({
  id: 0,
  name: '',
  avatar: '/app/images/profile/default-avatar.png'
})

const bindStatus = ref('pending') // pending, success, already_bound, conflict, failed
const binding = ref(false)
const loading = ref(true)
const currentReferrerName = ref('')

// 获取推荐人ID
const referrerId = computed(() => {
  return parseInt(route.params.referrerId) || 0
})

// 获取推荐人信息
const fetchReferrerInfo = async () => {
  if (!referrerId.value) {
    Toast.fail('无效的邀请链接')
    return
  }

  try {
    const res = await getReferrerInfo(referrerId.value)
    
    if (res.code === 0) {
      referrerInfo.value = res.data
    } else {
      Toast.fail(res.message || '获取推荐人信息失败')
    }
  } catch (error) {
    console.error('获取推荐人信息失败:', error)
    Toast.fail('网络错误，请重试')
  } finally {
    loading.value = false
  }
}

// 绑定推荐关系
const bindReferrerRelation = async () => {
  if (binding.value) return
  
  binding.value = true
  
  try {
    const res = await bindReferrer({
      referrer_id: referrerId.value,
      bind_type: 'immediate'
    })
    
    if (res.code === 0) {
      const data = res.data
      bindStatus.value = data.bind_status
      
      if (data.bind_status === 'success') {
        Toast.success('推荐关系绑定成功！')
      } else if (data.bind_status === 'already_bound') {
        Toast.success('您已经是该用户的下级了')
      } else if (data.bind_status === 'conflict') {
        // 保存当前推荐人名称
        if (data.current_referrer_name) {
          currentReferrerName.value = data.current_referrer_name
        }
        Toast.fail(data.message || '您已经有推荐人，不能修改')
      } else {
        Toast.fail(data.message || '绑定失败')
      }
    } else {
      Toast.fail(res.message || '绑定失败')
    }
  } catch (error) {
    console.error('绑定推荐关系失败:', error)
    Toast.fail('网络错误，请重试')
  } finally {
    binding.value = false
  }
}

// 获取状态图标
const getStatusIcon = () => {
  switch (bindStatus.value) {
    case 'success':
    case 'already_bound':
      return 'success'
    case 'conflict':
    case 'failed':
      return 'close'
    default:
      return 'info-o'
  }
}

// 获取状态颜色
const getStatusColor = () => {
  switch (bindStatus.value) {
    case 'success':
    case 'already_bound':
      return '#52c41a'
    case 'conflict':
    case 'failed':
      return '#ee0a24'
    default:
      return '#1989fa'
  }
}

// 获取状态文本
const getStatusText = () => {
  switch (bindStatus.value) {
    case 'success':
      return '推荐关系绑定成功！'
    case 'already_bound':
      return '您已经是该用户的下级了'
    case 'conflict':
      return currentReferrerName.value ? `您的推荐人是：${currentReferrerName.value}，不可修改` : '您已经有推荐人，不能修改'
    case 'failed':
      return '绑定失败，请重试'
    default:
      return ''
  }
}

// 页面跳转
const goBack = () => {
  router.back()
}

const goToLogin = () => {
  // 保存当前页面URL，登录后跳转回来
  const currentUrl = route.fullPath
  router.push(`/login?redirect=${encodeURIComponent(currentUrl)}`)
}

const goToRegister = () => {
  // 保存推荐人ID到URL，注册后跳转回来
  const currentUrl = route.fullPath
  router.push(`/register?redirect=${encodeURIComponent(currentUrl)}`)
}

const goToVipUpgrade = async () => {
  // 添加详细的调试信息
  console.log('=== VIP升级按钮被点击 ===')
  console.log('当前时间:', new Date().toLocaleString())
  console.log('用户登录状态:', userStore.isLoggedIn)
  console.log('用户信息:', userStore.user)
  console.log('绑定状态:', bindStatus.value)
  console.log('当前处理状态:', window._isProcessingVipUpgrade)

  // 检查是否已经在处理中
  if (window._isProcessingVipUpgrade) {
    console.log('⚠️ 正在处理VIP升级，跳过重复点击')
    Toast.fail('正在处理中，请稍候...')
    return
  }

  console.log('✅ 开始处理VIP升级')
  window._isProcessingVipUpgrade = true
  
  try {
    if (!userStore.isLoggedIn) {
      console.log('用户未登录，跳转到登录页面')
      Toast.fail('请先登录')
      goToLogin()
      // 确保清除处理标志
      window._isProcessingVipUpgrade = false
      return
    }

    // 检查用户VIP状态（用于调试）
    console.log('用户VIP状态:', {
      is_vip: userStore.user?.is_vip,
      is_vip_paid: userStore.user?.is_vip_paid,
      referrer_id: userStore.user?.referrer_id,
      wechat_openid: userStore.user?.wechat_openid
    })

    // 检查用户是否已经是VIP
    if (userStore.user?.is_vip && userStore.user?.is_vip_paid) {
      console.log('用户已经是VIP会员')
      Toast.success('您已经是VIP会员了')
      // 确保清除处理标志
      window._isProcessingVipUpgrade = false
      return
    }

    // 获取用户的openid
    let openid = userStore.user?.wechat_openid
    console.log('用户openid:', openid)
    
    if (!openid) {
      console.log('用户没有微信openid，尝试生成临时openid')
      // 如果没有openid，尝试获取微信授权
      Toast.loading({
        message: '正在获取微信授权...',
        forbidClick: true,
        duration: 3000
      })

      // 引导用户重新授权或使用临时openid
      openid = `temp_${userStore.user?.id}_${Date.now()}`
      console.log('生成临时openid:', openid)

      Toast.clear()
      Toast.fail('检测到您未完成微信授权，请稍后重试或联系客服')
      // 确保清除处理标志
      window._isProcessingVipUpgrade = false
      return
    }

    console.log('开始创建VIP订单...')
    Toast.loading({
      message: '正在创建订单...',
      forbidClick: true,
      duration: 0
    })

    // 创建VIP订单
    const orderData = {
      openid: openid,
      referrer_id: referrerId.value || 0
    }

    console.log('订单数据:', orderData)
    const res = await createVipOrder(orderData)
    console.log('创建订单API响应:', res)
    
    Toast.clear()

    if (res.code === 0) {
      const { payment_params, order_no, amount } = res.data
      console.log('订单创建成功:', { order_no, amount })
      console.log('支付参数:', payment_params)

      // 检查支付参数是否存在
      if (!payment_params) {
        console.error('❌ 支付参数缺失')
        Toast.fail('支付参数获取失败，请联系客服')
        return
      }

      // 发起微信支付
      if (typeof WeixinJSBridge !== 'undefined') {
        console.log('调用微信支付...')
        WeixinJSBridge.invoke(
          'getBrandWCPayRequest',
          payment_params,
          function(result) {
            console.log('微信支付回调结果:', result)
            if (result.err_msg === 'get_brand_wcpay_request:ok') {
              Toast.success('支付成功！正在为您升级VIP...')
              
              // 延迟跳转，让用户看到成功提示
              setTimeout(() => {
                router.push('/vip/success')
              }, 2000)
            } else if (result.err_msg === 'get_brand_wcpay_request:cancel') {
              Toast.fail('支付已取消')
            } else {
              Toast.fail('支付失败，请重试')
            }
          }
        )
      } else {
        console.log('非微信环境')
        // 非微信环境，显示提示
        Toast.fail('请在微信中打开此页面进行支付')
      }
    } else {
      console.log('创建订单失败:', res.message)
      Toast.fail(res.message || '创建订单失败')
    }
  } catch (error) {
    Toast.clear()
    console.error('创建VIP订单失败:', error)
    Toast.fail('网络错误，请重试')
  } finally {
    window._isProcessingVipUpgrade = false
  }
}

// 检查当前用户的绑定状态
const checkUserBindStatus = async () => {
  if (!userStore.isLoggedIn) {
    bindStatus.value = 'pending'
    return
  }

  // 检查当前用户是否已经有推荐人
  const currentUser = userStore.user
  if (currentUser && currentUser.referrer_id > 0) {
    if (currentUser.referrer_id == referrerId.value) {
      bindStatus.value = 'already_bound'
    } else {
      bindStatus.value = 'conflict'
      // 如果有推荐人但不是当前的，尝试通过API获取当前推荐人信息
      try {
        const res = await bindReferrer({
          referrer_id: referrerId.value,
          bind_type: 'immediate'
        })
        if (res.code === 0 && res.data.current_referrer_name) {
          currentReferrerName.value = res.data.current_referrer_name
        }
      } catch (error) {
        console.error('获取当前推荐人信息失败:', error)
      }
    }
  } else {
    bindStatus.value = 'pending'
  }
}

// 检查登录状态和自动绑定
const checkAndBindIfLoggedIn = async () => {
  await checkUserBindStatus()
  
  if (userStore.isLoggedIn && referrerId.value && bindStatus.value === 'pending') {
    // 如果已登录且有推荐人ID且没有绑定过，自动尝试绑定
    await bindReferrerRelation()
  }
}

// 页面初始化
onMounted(async () => {
  // 清除可能残留的处理标志
  window._isProcessingVipUpgrade = false
  console.log('🔄 页面初始化，清除VIP升级处理标志')

  await fetchReferrerInfo()
  await checkAndBindIfLoggedIn()
})
</script>

<style scoped>
.landing-page {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.page-content {
  padding: 16px;
  padding-bottom: 32px;
}

/* 推荐人信息卡片 */
.referrer-card {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 16px;
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.referrer-info {
  display: flex;
  align-items: center;
}

.referrer-details {
  margin-left: 16px;
  flex: 1;
}

.referrer-name {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 4px;
}

.referrer-title {
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 8px;
}

.invite-text {
  font-size: 16px;
  font-weight: 500;
}

/* VIP介绍卡片 */
.intro-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.intro-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.intro-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-left: 8px;
}

.privilege-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.privilege-item {
  text-align: center;
  padding: 16px 8px;
}

.privilege-item .van-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.privilege-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.privilege-desc {
  font-size: 12px;
  color: #666;
}

/* 价格卡片 */
.price-card {
  background: linear-gradient(135deg, #ff9500, #ff6b35);
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 16px;
  color: white;
  text-align: center;
  box-shadow: 0 4px 12px rgba(255, 149, 0, 0.3);
}

.price-header {
  margin-bottom: 20px;
}

.price-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
}

.price-subtitle {
  font-size: 14px;
  opacity: 0.9;
}

.price-main {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 12px;
}

.price-symbol {
  font-size: 24px;
  font-weight: 600;
}

.price-value {
  font-size: 48px;
  font-weight: bold;
  margin-left: 4px;
}

.price-desc {
  font-size: 16px;
  margin-bottom: 16px;
  opacity: 0.9;
}

.price-benefits {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.benefit-item {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.benefit-item .van-icon {
  margin-right: 6px;
}

/* 绑定状态卡片 */
.bind-status-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.status-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-text {
  margin-left: 8px;
  font-size: 14px;
  font-weight: 500;
}

/* 操作区域 */
.action-section {
  margin-bottom: 16px;
}

.login-prompt {
  text-align: center;
}

.prompt-text {
  font-size: 16px;
  color: #333;
  margin-bottom: 20px;
}

.login-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 温馨提示 */
.tips-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.tips-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.tips-title .van-icon {
  margin-right: 8px;
  color: #1989fa;
}

.tips-content {
  space-y: 8px;
}

.tip-item {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 8px;
}
</style> 