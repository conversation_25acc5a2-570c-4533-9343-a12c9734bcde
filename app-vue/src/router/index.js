import { createRouter, createWebHashHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'

// 路由懒加载
const Home = () => import('../views/home/<USER>')
const Dashboard = () => import('../views/home/<USER>')
const Device = () => import('../views/device/Index.vue')
const WaterPoint = () => import('../views/water-point/Index.vue')
const Merchant = () => import('../views/merchant/Index.vue')
const User = () => import('../views/user/Index.vue')

// 设备相关页面
const DeviceDetail = () => import('../views/device/Detail.vue')
const DeviceInstall = () => import('../views/device/Install.vue')
const DeviceMonitor = () => import('../views/device/Monitor.vue')
const MyDevices = () => import('../views/device/MyDevices.vue')

// 取水点相关页面
const WaterPointDetail = () => import('../views/water-point/Detail.vue')
const WaterPointMap = () => import('../views/water-point/Map.vue')

// 商家相关页面
const MerchantDetail = () => import('../views/merchant/Detail.vue')
const MerchantProducts = () => import('../views/merchant/Products.vue')
const MerchantOrders = () => import('../views/merchant/Orders.vue')
const MerchantDashboard = () => import('../views/merchant/Dashboard.vue')
const MerchantTrades = () => import('../views/merchant/Trades.vue')
const MerchantTradeDetail = () => import('../views/merchant/TradeDetail.vue')
const MerchantQRCode = () => import('../views/merchant/QRCode.vue')

// 用户相关页面
const UserOrders = () => import('../views/user/Orders.vue')
const UserWallet = () => import('../views/user/Wallet.vue')
const UserSettings = () => import('../views/user/Settings.vue')
const Login = () => import('../views/user/Login.vue')
const Register = () => import('../views/user/Register.vue')
const ForgotPassword = () => import('../views/user/ForgotPassword.vue')
const UserAddress = () => import('../views/user/Address.vue')
const UserPoints = () => import('../views/user/Points.vue')
const UserCoupons = () => import('../views/user/Coupons.vue')
const UserFeedback = () => import('../views/user/Feedback.vue')
const UserHelp = () => import('../views/user/Help.vue')
const BindPhone = () => import('../views/user/BindPhone.vue')
const WechatCallback = () => import('../views/user/WechatCallback.vue')
const WechatSuccess = () => import('../views/user/WechatSuccess.vue')
const WechatError = () => import('../views/user/WechatError.vue')
// 分支机构相关页面
const BranchLogin = () => import('../views/branch/Login.vue')
const BranchHome = () => import('../views/branch/Home.vue')
const BranchProfile = () => import('../views/branch/Profile.vue')
const BranchWechatSuccess = () => import('../views/branch/WechatSuccess.vue')
const BranchWechatMenu = () => import('../views/branch/WechatMenu.vue')

// 分支机构功能页面
const BranchWallet = () => import('../views/branch/Wallet.vue')
const BranchOrders = () => import('../views/branch/Orders.vue')
const BranchAddress = () => import('../views/branch/Address.vue')
const BranchProducts = () => import('../views/branch/Products.vue')
const BranchEvents = () => import('../views/branch/Events.vue')
const BranchSupport = () => import('../views/branch/Support.vue')
const BranchCommunity = () => import('../views/branch/Community.vue')
const BranchNews = () => import('../views/branch/News.vue')
const BranchContact = () => import('../views/branch/Contact.vue')
const BranchSettings = () => import('../views/branch/Settings.vue')
const BranchFeedback = () => import('../views/branch/Feedback.vue')
const BranchHelp = () => import('../views/branch/Help.vue')
const BranchDevices = () => import('../views/branch/Devices.vue')
const BranchPoints = () => import('../views/branch/Points.vue')
const BranchBenefits = () => import('../views/branch/Benefits.vue')
const BranchBooking = () => import('../views/branch/Booking.vue')
const BranchMembers = () => import('../views/branch/Members.vue')
const BranchRevenue = () => import('../views/branch/Revenue.vue')
const BranchMore = () => import('../views/branch/More.vue')

// 分支机构工作台页面
const BranchSalesmanIndex = () => import('../views/branch/salesman/Index.vue')
const BranchVipIndex = () => import('../views/branch/vip/Index.vue')
const BranchVipTeam = () => import('../views/branch/vip/Team.vue')
const BranchVipDividend = () => import('../views/branch/vip/Dividend.vue')
const BranchVipDividendHistory = () => import('../views/branch/vip/DividendHistory.vue')
const BranchVipDividendDetail = () => import('../views/branch/vip/DividendDetail.vue')
const BranchVipRecruitRanking = () => import('../views/branch/vip/RecruitRanking.vue')
const BranchVipTeamDevices = () => import('../views/branch/vip/TeamDevices.vue')
const BranchVipList = () => import('../views/branch/vip/VipList.vue')
const BranchVipDividendRanking = () => import('../views/branch/vip/DividendRanking.vue')
const BranchVipRewardList = () => import('../views/branch/vip/RewardList.vue')
const BranchWaterIndex = () => import('../views/branch/water/Index.vue')
const BranchEngineerIndex = () => import('../views/branch/engineer/Index.vue')

// 用户设置相关页面
const UserProfile = () => import('../views/user/Settings/Profile.vue')
const UserVerification = () => import('../views/user/Settings/Verification.vue')
const UserSecurity = () => import('../views/user/Settings/Security.vue')
const UserChangePassword = () => import('../views/user/Settings/ChangePassword.vue')
const UserChangePhone = () => import('../views/user/Settings/ChangePhone.vue')
const UserBankCard = () => import('../views/user/Settings/BankCard.vue')
const UserAbout = () => import('../views/user/Settings/About.vue')
const UserPayment = () => import('../views/user/Settings/Payment.vue')

// 工程师工作台页面
const EngineerIndex = () => import('../views/engineer/Index.vue')
const EngineerOrders = () => import('../views/engineer/Orders.vue')

// 净水器用户工作台页面
const PurifierIndex = () => import('../views/purifier/Index.vue')
const PurifierDevices = () => import('../views/purifier/devices/Index.vue')
const PurifierDeviceDetail = () => import('../views/purifier/device-detail/Index.vue')
const PurifierDeviceMonitor = () => import('../views/purifier/device-monitor/Index.vue')
const FilterShop = () => import('../views/purifier/FilterShop.vue')

// 支付机构工作台页面
const InstitutionIndex = () => import('../views/institution/Index.vue')

// 净水器渠道商工作台页面
const AgentIndex = () => import('../views/agent/Index.vue')

// VIP会员工作台页面
const VipIndex = () => import('../views/vip/Index.vue')

// 业务员工作台页面
const SalesmanIndex = () => import('../views/salesman/Index.vue')

// 管理员后台页面
const AdminIndex = () => import('../views/admin/Index.vue')

// 管理员商户管理页面
const AdminMerchants = () => import('../views/admin/merchants/Index.vue')
const AdminMerchantTrades = () => import('../views/admin/merchants/Trades.vue')
const AdminMerchantSettlements = () => import('../views/admin/merchants/Settlements.vue')

// 支付机构组件
const Income = () => import('../views/institution/Income.vue')

// 代理商组件
const Team = () => import('../views/agent/Team.vue')

// VIP会员组件
const Dividend = () => import('../views/vip/Dividend.vue')
const VipTeam = () => import('../views/vip/Team.vue')
const RewardList = () => import('../views/vip/RewardList.vue')

// 业务员组件
const Clients = () => import('../views/salesman/Clients.vue')
const Commission = () => import('../views/salesman/Commission.vue')
const InviteInstall = () => import('../views/salesman/InviteInstall.vue')

// 管理员组件
const Users = () => import('../views/admin/Users.vue')

// 预约安装相关页面
const InstallBooking = () => import('../views/installation/Booking.vue')
const BookingPayment = () => import('../views/installation/BookingPayment.vue')
const BookingList = () => import('../views/installation/BookingList.vue')

// 测试页面
const WeChatTest = () => import('../views/test/WeChatTest.vue')
const BranchWechatTest = () => import('../views/test/BranchWechatTest.vue')
const WechatMenuTest = () => import('../views/test/WechatMenuTest.vue')

// 邀请函相关路由
const invitationRoutes = [
  {
    path: '/invitation/:id',
    name: 'Invitation',
    component: () => import('../views/invitation/index.vue'),
    meta: {
      title: '点点够开业庆典邀请函',
      hideTabBar: true,
      requiresAuth: false,
    },
  },
];

// VIP会员相关路由
const vipRoutes = [
  {
    path: '/vip/dividend',
    name: 'VipDividend',
    component: Dividend,
    meta: {
      title: '平台分红收益明细',
      requiresAuth: false,
      hideTabBar: true
    }
  },
  {
    path: '/vip/team',
    name: 'VipTeam',
    component: VipTeam,
    meta: {
      title: 'VIP团队',
      requiresAuth: true,
      roles: ['vip'],
      hideTabBar: true
    }
  },
  {
    path: '/vip/team-detail/:id',
    name: 'VipTeamDetail',
    component: () => import('../views/vip/TeamDetail.vue'),
    meta: {
      title: '团队成员详情',
      requiresAuth: true,
      roles: ['vip'],
      hideTabBar: true
    }
  },
  {
    path: '/vip/dividend-history',
    name: 'VipDividendHistory',
    component: () => import('../views/vip/DividendHistory.vue'),
    meta: {
      title: '分红历史',
      requiresAuth: true,
      roles: ['vip'],
      hideTabBar: true
    }
  },
  {
    path: '/vip/dividend-detail/:id',
    name: 'VipDividendDetail',
    component: () => import('../views/vip/DividendDetail.vue'),
    meta: {
      title: '分红详情',
      requiresAuth: true,
      roles: ['vip'],
      hideTabBar: true
    }
  },
  {
    path: '/vip/reward-list',
    name: 'RewardList',
    component: RewardList,
    meta: {
      title: '分红奖励名单',
      requiresAuth: true,
      roles: ['vip'],
      hideTabBar: true
    }
  },
  {
    path: '/vip/recruit-ranking',
    name: 'RecruitRanking',
    component: () => import('../views/vip/RecruitRanking.vue'),
    meta: {
      title: 'VIP招募排行榜',
      requiresAuth: true,
      roles: ['vip'],
      hideTabBar: true
    }
  },
  {
    path: '/vip/team-devices',
    name: 'VipTeamDevices',
    component: () => import('../views/vip/TeamDevices.vue'),
    meta: {
      title: '团队销售净水器',
      requiresAuth: true,
      roles: ['vip'],
      hideTabBar: true
    }
  },
  {
    path: '/vip/recruit',
    name: 'VipRecruit',
    component: () => import('../views/vip/Recruit.vue'),
    meta: {
      title: 'VIP招募',
      requiresAuth: true,
      roles: ['vip'],
      hideTabBar: true
    }
  },
  {
    path: '/vip/recruit-landing/:referrerId',
    name: 'VipRecruitLanding',
    component: () => import('../views/vip/RecruitLanding.vue'),
    meta: {
      title: 'VIP招募',
      requiresAuth: false,
      hideTabBar: true
    }
  },
  {
    path: '/vip/success',
    name: 'VipSuccess',
    component: () => import('../views/vip/Success.vue'),
    meta: {
      title: 'VIP升级成功',
      requiresAuth: false,
      hideTabBar: true
    }
  },
]

// 引入VIP会员相关组件
const VipList = () => import('../views/vip/VipList.vue')
const VipDetail = () => import('../views/vip/VipDetail.vue')

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: '点点够首页',
      keepAlive: true,
      shareSubtitle: '让净水共享更便捷，每天进步一点点',
      shareLogo: 'https://pay.itapgo.com/images/logo.png'
    }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: {
      title: '控制面板',
      keepAlive: true,
      requiresAuth: true,
      shareSubtitle: '统一工作台入口，管理您的业务'
    }
  },
  {
    path: '/device',
    name: 'Device',
    component: Device,
    meta: {
      title: '设备管理',
      keepAlive: true,
      shareSubtitle: '净水设备管理平台，轻松管理您的设备'
    }
  },
  {
    path: '/device/detail/:id',
    name: 'DeviceDetail',
    component: DeviceDetail,
    meta: {
      title: '设备详情',
      hideTabBar: true
    }
  },
  {
    path: '/device/install',
    name: 'DeviceInstall',
    component: DeviceInstall,
    meta: {
      title: '安装设备',
      hideTabBar: true
    }
  },
  {
    path: '/device/monitor/:id',
    name: 'DeviceMonitor',
    component: DeviceMonitor,
    meta: {
      title: '设备监控',
      hideTabBar: true
    }
  },
  {
    path: '/device/my-devices',
    name: 'MyDevices',
    component: MyDevices,
    meta: {
      title: '我的设备',
      requiresAuth: true,
      roles: ['water_purifier_user'],
      hideTabBar: true
    }
  },
  {
    path: '/water-point',
    name: 'WaterPoint',
    component: WaterPoint,
    meta: {
      title: '取水点',
      keepAlive: true,
      shareSubtitle: '寻找附近取水点，享受健康净水'
    }
  },
  {
    path: '/water-point/detail/:id',
    name: 'WaterPointDetail',
    component: WaterPointDetail,
    meta: {
      title: '取水点详情',
      shareSubtitle: '查看取水点详细信息和用户评价'
    }
  },
  {
    path: '/water-point/map',
    name: 'WaterPointMap',
    component: WaterPointMap,
    meta: {
      title: '取水点地图',
      shareSubtitle: '地图上查找附近的取水点位置'
    }
  },
  {
    path: '/merchant',
    name: 'Merchant',
    component: Merchant,
    meta: {
      title: '商家',
      keepAlive: true,
      shareSubtitle: '净水服务商家，提供优质净水服务'
    }
  },
  {
    path: '/merchant/detail/:id',
    name: 'MerchantDetail',
    component: MerchantDetail,
    meta: {
      title: '商家详情',
      shareSubtitle: '了解商家详细信息和服务内容'
    }
  },
  {
    path: '/merchant/dashboard',
    name: 'MerchantDashboard',
    component: MerchantDashboard,
    meta: {
      title: '商户工作台',
      requiresAuth: true,
      shareSubtitle: '商户数据管理中心，实时掌握经营状况'
    }
  },
  {
    path: '/merchant/trades',
    name: 'MerchantTrades',
    component: MerchantTrades,
    meta: {
      title: '交易记录',
      requiresAuth: true,
      shareSubtitle: '查看商户交易明细，清晰记录收支情况'
    }
  },
  {
    path: '/merchant/trade/:tradeNo',
    name: 'MerchantTradeDetail',
    component: MerchantTradeDetail,
    meta: {
      title: '交易详情',
      requiresAuth: true
    }
  },
  {
    path: '/merchant/products',
    name: 'MerchantProducts',
    component: MerchantProducts,
    meta: {
      title: '商品管理',
      requiresAuth: true
    }
  },
  {
    path: '/merchant/orders',
    name: 'MerchantOrders',
    component: MerchantOrders,
    meta: {
      title: '订单管理'
    }
  },
  {
    path: '/merchant/qrcode',
    name: 'MerchantQRCode',
    component: MerchantQRCode,
    meta: {
      title: '商户收款码',
      requiresAuth: true,
      shareSubtitle: '扫描二维码向此商户付款，支持微信支付和支付宝',
      shareLogo: 'https://pay.itapgo.com/images/logo.png'
    }
  },
  {
    path: '/merchant/notifications',
    name: 'MerchantNotifications',
    component: () => import('../views/merchant/Notifications.vue'),
    meta: {
      title: '通知消息',
      requiresAuth: true,
      hideTabBar: true
    }
  },
  {
    path: '/user',
    name: 'User',
    component: User,
    meta: {
      title: '个人中心',
      keepAlive: true,
      requiresAuth: true
    }
  },
  {
    path: '/user/orders',
    name: 'UserOrders',
    component: UserOrders,
    meta: {
      title: '我的订单'
    }
  },
  {
    path: '/user/wallet',
    name: 'UserWallet',
    component: UserWallet,
    meta: {
      title: '我的钱包'
    }
  },
  {
    path: '/user/settings',
    name: 'UserSettings',
    component: UserSettings,
    meta: {
      title: '设置',
      requiresAuth: true,
      hideTabBar: true
    }
  },
  {
    path: '/user/settings/profile',
    name: 'UserProfile',
    component: UserProfile,
    meta: {
      title: '个人资料',
      requiresAuth: true,
      hideTabBar: true
    }
  },
  {
    path: '/user/settings/verification',
    name: 'UserVerification',
    component: UserVerification,
    meta: {
      title: '实名认证',
      requiresAuth: true,
      hideTabBar: true
    }
  },
  {
    path: '/user/settings/security',
    name: 'UserSecurity',
    component: UserSecurity,
    meta: {
      title: '账号安全',
      requiresAuth: true,
      hideTabBar: true
    }
  },
  {
    path: '/user/settings/change-password',
    name: 'UserChangePassword',
    component: UserChangePassword,
    meta: {
      title: '修改密码',
      requiresAuth: true,
      hideTabBar: true
    }
  },
  {
    path: '/user/settings/change-phone',
    name: 'UserChangePhone',
    component: UserChangePhone,
    meta: {
      title: '修改手机号',
      requiresAuth: true,
      hideTabBar: true
    }
  },
  {
    path: '/user/settings/payment',
    name: 'UserPayment',
    component: UserPayment,
    meta: {
      title: '支付管理',
      requiresAuth: true,
      hideTabBar: true
    }
  },
  {
    path: '/user/settings/bank-card',
    name: 'UserBankCard',
    component: UserBankCard,
    meta: {
      title: '提现/结算管理',
      requiresAuth: true,
      hideTabBar: true
    }
  },
  {
    path: '/user/settings/about',
    name: 'UserAbout',
    component: UserAbout,
    meta: {
      title: '关于我们',
      requiresAuth: true,
      hideTabBar: true
    }
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: '用户登录'
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    meta: {
      title: '用户注册'
    }
  },
  {
    path: '/forgot-password',
    name: 'ForgotPassword',
    component: ForgotPassword,
    meta: {
      title: '忘记密码'
    }
  },
  {
    path: '/bind-phone',
    name: 'BindPhone',
    component: BindPhone,
    meta: {
      title: '绑定手机号',
      requiresAuth: true,
      hideTabBar: true
    }
  },
  {
    path: '/wechat-callback',
    name: 'WechatCallback',
    component: WechatCallback,
    meta: {
      title: '微信登录',
      hideNavBar: true,
      hideTabBar: true
    }
  },
  {
    path: '/wechat-callback-install',
    name: 'WechatCallbackInstall',
    component: () => import('../views/installation/WechatCallbackInstall.vue'),
    meta: {
      title: '微信登录(安装流程)',
      hideNavBar: true,
      hideTabBar: true
    }
  },
  {
    path: '/wechat-success',
    name: 'WechatSuccess',
    component: WechatSuccess,
    meta: {
      title: '微信登录成功',
      hideNavBar: true,
      hideTabBar: true
    }
  },
  {
    path: '/wechat-error',
    name: 'WechatError',
    component: WechatError,
    meta: {
      title: '微信登录失败',
      hideNavBar: true,
      hideTabBar: true
    }
  },
  {
    path: '/branch-login',
    name: 'BranchLogin',
    component: BranchLogin,
    meta: {
      title: '分支机构登录',
      hideNavBar: true,
      hideTabBar: true,
      requiresAuth: false
    }
  },
  {
    path: '/branch/wechat-menu',
    name: 'BranchWechatMenu',
    component: BranchWechatMenu,
    meta: {
      title: '微信菜单管理',
      hideTabBar: true,
      requiresAuth: true,
      roles: ['branch']
    }
  },
  {
    path: '/branch/profile',
    name: 'BranchProfile',
    component: BranchProfile,
    meta: {
      title: '分支机构用户中心',
      hideTabBar: true,
      requiresAuth: false  // 分支机构用户中心页面内部处理登录状态
    }
  },
  {
    path: '/branch/wechat-success',
    name: 'BranchWechatSuccess',
    component: BranchWechatSuccess,
    meta: {
      title: '分支机构微信登录成功',
      hideNavBar: true,
      hideTabBar: true,
      requiresAuth: false
    }
  },
  // 分支机构首页
  {
    path: '/branch/home',
    name: 'BranchHome',
    component: BranchHome,
    meta: {
      title: '分支机构首页',
      hideTabBar: true,
      requiresAuth: false
    }
  },
  // 分支机构功能页面
  {
    path: '/branch/wallet',
    name: 'BranchWallet',
    component: BranchWallet,
    meta: {
      title: '我的钱包',
      hideTabBar: true,
      requiresAuth: true
    }
  },
  {
    path: '/branch/orders',
    name: 'BranchOrders',
    component: BranchOrders,
    meta: {
      title: '我的订单',
      hideTabBar: true,
      requiresAuth: true
    }
  },
  {
    path: '/branch/address',
    name: 'BranchAddress',
    component: BranchAddress,
    meta: {
      title: '收货地址',
      hideTabBar: true,
      requiresAuth: true
    }
  },
  {
    path: '/branch/products',
    name: 'BranchProducts',
    component: BranchProducts,
    meta: {
      title: '商品服务',
      hideTabBar: true,
      requiresAuth: false
    }
  },
  {
    path: '/branch/events',
    name: 'BranchEvents',
    component: BranchEvents,
    meta: {
      title: '活动专区',
      hideTabBar: true,
      requiresAuth: false
    }
  },
  {
    path: '/branch/support',
    name: 'BranchSupport',
    component: BranchSupport,
    meta: {
      title: '客户服务',
      hideTabBar: true,
      requiresAuth: false
    }
  },
  {
    path: '/branch/community',
    name: 'BranchCommunity',
    component: BranchCommunity,
    meta: {
      title: '社区互动',
      hideTabBar: true,
      requiresAuth: false
    }
  },
  {
    path: '/branch/news',
    name: 'BranchNews',
    component: BranchNews,
    meta: {
      title: '分支动态',
      hideTabBar: true,
      requiresAuth: false
    }
  },
  {
    path: '/branch/contact',
    name: 'BranchContact',
    component: BranchContact,
    meta: {
      title: '联系我们',
      hideTabBar: true,
      requiresAuth: false
    }
  },
  {
    path: '/branch/settings',
    name: 'BranchSettings',
    component: BranchSettings,
    meta: {
      title: '设置',
      hideTabBar: true,
      requiresAuth: true
    }
  },
  {
    path: '/branch/feedback',
    name: 'BranchFeedback',
    component: BranchFeedback,
    meta: {
      title: '用户反馈',
      hideTabBar: true,
      requiresAuth: true
    }
  },
  {
    path: '/branch/help',
    name: 'BranchHelp',
    component: BranchHelp,
    meta: {
      title: '帮助中心',
      hideTabBar: true,
      requiresAuth: false
    }
  },
  {
    path: '/branch/devices',
    name: 'BranchDevices',
    component: BranchDevices,
    meta: {
      title: '我的设备',
      hideTabBar: true,
      requiresAuth: true
    }
  },
  {
    path: '/branch/points',
    name: 'BranchPoints',
    component: BranchPoints,
    meta: {
      title: '我的积分',
      hideTabBar: true,
      requiresAuth: true
    }
  },
  {
    path: '/branch/benefits',
    name: 'BranchBenefits',
    component: BranchBenefits,
    meta: {
      title: '专属福利',
      hideTabBar: true,
      requiresAuth: true
    }
  },
  {
    path: '/branch/booking',
    name: 'BranchBooking',
    component: BranchBooking,
    meta: {
      title: '预约服务',
      hideTabBar: true,
      requiresAuth: false
    }
  },
  {
    path: '/branch/members',
    name: 'BranchMembers',
    component: BranchMembers,
    meta: {
      title: '会员管理',
      hideTabBar: true,
      requiresAuth: true
    }
  },
  {
    path: '/branch/revenue',
    name: 'BranchRevenue',
    component: BranchRevenue,
    meta: {
      title: '营收统计',
      hideTabBar: true,
      requiresAuth: true
    }
  },
  {
    path: '/branch/more',
    name: 'BranchMore',
    component: BranchMore,
    meta: {
      title: '更多服务',
      hideTabBar: true,
      requiresAuth: false
    }
  },
  // 分支机构工作台路由
  {
    path: '/branch/salesman',
    name: 'BranchSalesman',
    component: BranchSalesmanIndex,
    meta: {
      title: '业务中心',
      hideTabBar: true,
      requiresAuth: true
    }
  },
  {
    path: '/branch/vip',
    name: 'BranchVip',
    component: BranchVipIndex,
    meta: {
      title: 'VIP中心',
      hideTabBar: true,
      requiresAuth: true
    }
  },
  {
    path: '/branch/vip/team',
    name: 'BranchVipTeam',
    component: BranchVipTeam,
    meta: {
      title: '我的VIP团队',
      hideTabBar: true,
      requiresAuth: true
    }
  },
  {
    path: '/branch/vip/dividend',
    name: 'BranchVipDividend',
    component: BranchVipDividend,
    meta: {
      title: 'VIP分红',
      hideTabBar: true,
      requiresAuth: true
    }
  },
  {
    path: '/branch/vip/dividend-history',
    name: 'BranchVipDividendHistory',
    component: BranchVipDividendHistory,
    meta: {
      title: '分红历史',
      hideTabBar: true,
      requiresAuth: true
    }
  },
  {
    path: '/branch/vip/dividend-detail/:id',
    name: 'BranchVipDividendDetail',
    component: BranchVipDividendDetail,
    meta: {
      title: '分红详情',
      hideTabBar: true,
      requiresAuth: true
    }
  },
  {
    path: '/branch/vip/recruit-ranking',
    name: 'BranchVipRecruitRanking',
    component: BranchVipRecruitRanking,
    meta: {
      title: 'VIP招募排行榜',
      hideTabBar: true,
      requiresAuth: true
    }
  },
  {
    path: '/branch/vip/team-devices',
    name: 'BranchVipTeamDevices',
    component: BranchVipTeamDevices,
    meta: {
      title: '团队销售净水器',
      hideTabBar: true,
      requiresAuth: true
    }
  },
  {
    path: '/branch/vip/vip-list',
    name: 'BranchVipList',
    component: BranchVipList,
    meta: {
      title: 'VIP列表',
      hideTabBar: true,
      requiresAuth: true
    }
  },
  {
    path: '/branch/vip/dividend-ranking',
    name: 'BranchVipDividendRanking',
    component: BranchVipDividendRanking,
    meta: {
      title: '分红排行',
      hideTabBar: true,
      requiresAuth: true
    }
  },
  {
    path: '/branch/vip/reward-list',
    name: 'BranchVipRewardList',
    component: BranchVipRewardList,
    meta: {
      title: '奖励记录',
      hideTabBar: true,
      requiresAuth: true
    }
  },
  {
    path: '/branch/vip/test',
    name: 'BranchVipTest',
    component: () => import('../views/branch/vip/VipTest.vue'),
    meta: {
      title: 'VIP测试',
      hideTabBar: true,
      requiresAuth: false
    }
  },
  {
    path: '/branch/vip/dividend-debug',
    name: 'BranchVipDividendDebug',
    component: () => import('../views/branch/vip/DividendDebug.vue'),
    meta: {
      title: '分红调试',
      hideTabBar: true,
      requiresAuth: false
    }
  },
  {
    path: '/branch/water',
    name: 'BranchWater',
    component: BranchWaterIndex,
    meta: {
      title: '净水管理',
      hideTabBar: true,
      requiresAuth: true
    }
  },
  {
    path: '/branch/engineer',
    name: 'BranchEngineer',
    component: BranchEngineerIndex,
    meta: {
      title: '工程师台',
      hideTabBar: true,
      requiresAuth: true
    }
  },
  {
    path: '/user/address',
    name: 'UserAddress',
    component: UserAddress,
    meta: {
      title: '我的地址'
    }
  },
  {
    path: '/user/points',
    name: 'UserPoints',
    component: UserPoints,
    meta: {
      title: '我的积分'
    }
  },
  {
    path: '/user/coupons',
    name: 'UserCoupons',
    component: UserCoupons,
    meta: {
      title: '我的优惠券'
    }
  },
  {
    path: '/user/feedback',
    name: 'UserFeedback',
    component: UserFeedback,
    meta: {
      title: '用户反馈'
    }
  },
  {
    path: '/user/help',
    name: 'UserHelp',
    component: UserHelp,
    meta: {
      title: '帮助中心'
    }
  },
  // 工程师工作台路由
  {
    path: '/engineer',
    name: 'Engineer',
    component: EngineerIndex,
    meta: {
      title: '工程师工作台',
      requiresAuth: true,
      roles: ['engineer']
    }
  },
  // 工程师子路由
  {
    path: '/engineer/orders',
    name: 'EngineerOrders',
    component: EngineerOrders,
    meta: {
      title: '工单管理',
      requiresAuth: true,
      roles: ['engineer']
    }
  },
  // 净水器用户工作台路由
  {
    path: '/purifier',
    name: 'Purifier',
    component: PurifierIndex,
    meta: {
      title: '净水器管理',
      requiresAuth: true,
      roles: ['water_purifier_user']
    }
  },
  {
    path: '/purifier/devices',
    name: 'PurifierDevices',
    component: PurifierDevices,
    meta: {
      title: '我的设备',
      requiresAuth: true,
      roles: ['water_purifier_user'],
      hideTabBar: true
    }
  },
  {
    path: '/purifier/device-detail/:id',
    name: 'PurifierDeviceDetail',
    component: PurifierDeviceDetail,
    meta: {
      title: '设备详情',
      requiresAuth: true
    }
  },
  {
    path: '/purifier/device-monitor/:id',
    name: 'PurifierDeviceMonitor',
    component: PurifierDeviceMonitor,
    meta: {
      title: '设备监控',
      requiresAuth: true
    }
  },
  {
    path: '/purifier/filter-shop',
    name: 'FilterShop',
    component: FilterShop,
    meta: {
      title: '滤芯商城',
      requiresAuth: true
    }
  },
  // 支付机构工作台路由
  {
    path: '/institution',
    name: 'Institution',
    component: InstitutionIndex,
    meta: {
      title: '支付机构',
      requiresAuth: true,
      roles: ['pay_institution']
    }
  },
  // 支付机构路由
  {
    path: '/institution/income',
    name: 'InstitutionIncome',
    component: Income,
    meta: {
      title: '收入明细'
    }
  },
  // 净水器渠道商工作台路由
  {
    path: '/agent',
    name: 'Agent',
    component: AgentIndex,
    meta: {
      title: '渠道商管理',
      requiresAuth: true,
      roles: ['water_purifier_agent']
    }
  },
  // 代理商路由
  {
    path: '/agent/team',
    name: 'AgentTeam',
    component: Team,
    meta: {
      title: '我的团队'
    }
  },
  // VIP会员工作台路由
  {
    path: '/vip',
    name: 'Vip',
    component: VipIndex,
    meta: {
      title: 'VIP会员中心',
      requiresAuth: true,
      roles: ['vip']
    }
  },
  // VIP会员路由
  ...vipRoutes,
  // 业务员工作台路由
  {
    path: '/salesman',
    name: 'Salesman',
    component: SalesmanIndex,
    meta: {
      title: '业务员中心',
      requiresAuth: false,  // 改为不需要认证，由页面内部处理
      // roles: ['salesman']  // 注释掉角色要求
    }
  },
  // 业务员路由
  {
    path: '/salesman/clients',
    name: 'SalesmanClients',
    component: Clients,
    meta: {
      title: '我的客户'
    }
  },
  {
    path: '/salesman/invite-install',
    name: 'SalesmanInviteInstall',
    component: InviteInstall,
    meta: {
      title: '邀请安装',
      hideTabBar: true
    }
  },
  {
    path: '/salesman/add-client',
    name: 'SalesmanAddClient',
    component: () => import('../views/salesman/add-client.vue'),
    meta: {
      title: '添加客户',
      hideTabBar: true
    }
  },
  {
    path: '/salesman/targets',
    name: 'SalesmanTargets',
    component: () => import('../views/salesman/Targets.vue'),
    meta: {
      title: '目标管理',
      hideTabBar: true
    }
  },
  {
    path: '/salesman/commission',
    name: 'SalesmanCommission',
    component: Commission,
    meta: {
      title: '佣金记录'
    }
  },
  {
    path: '/salesman/products',
    name: 'SalesmanProducts',
    component: () => import('../views/salesman/Products.vue'),
    meta: {
      title: '升级VIP',
      hideTabBar: true
    }
  },
  // 管理员后台路由
  {
    path: '/admin',
    name: 'Admin',
    component: AdminIndex,
    meta: {
      title: '管理后台',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  // 管理员路由
  {
    path: '/admin/users',
    name: 'AdminUsers',
    component: Users,
    meta: {
      title: '用户管理'
    }
  },
  {
    path: '/admin/nav-manager',
    name: 'NavManager',
    component: () => import('../views/admin/NavigationManager.vue'),
    meta: {
      requiresAuth: true,
      isAdmin: true
    }
  },
  // 管理员商户管理路由
  {
    path: '/admin/merchants',
    name: 'AdminMerchants',
    component: AdminMerchants,
    meta: {
      title: '商户管理',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/admin/merchants/mobile',
    name: 'AdminMerchantsMobile',
    component: () => import('../views/admin/merchants/MobileIndex.vue'),
    meta: {
      title: '商户管理(移动版)',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/admin/merchants/trades',
    name: 'AdminMerchantTrades',
    component: AdminMerchantTrades,
    meta: {
      title: '商户交易记录',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/admin/merchants/settlements',
    name: 'AdminMerchantSettlements',
    component: AdminMerchantSettlements,
    meta: {
      title: '商户结算记录',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/admin/merchants/trades/mobile',
    name: 'AdminMerchantTradesMobile',
    component: () => import('../views/admin/merchants/MobileTrades.vue'),
    meta: {
      title: '商户交易记录(移动版)',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/admin/merchants/settlements/mobile',
    name: 'AdminMerchantSettlementsMobile',
    component: () => import('../views/admin/merchants/MobileSettlements.vue'),
    meta: {
      title: '商户结算记录(移动版)',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  // 预约安装路由(公开访问)
  {
    path: '/installation/booking',
    name: 'InstallBooking',
    component: InstallBooking,
    meta: {
      title: '净水器预约安装',
      hideTabBar: true,
      requiresAuth: false,
      shareSubtitle: '预约专业工程师上门安装净水器，开启健康饮水生活'
    }
  },
  // 预约安装支付路由
  {
    path: '/installation/payment/:id',
    name: 'BookingPayment',
    component: BookingPayment,
    meta: {
      title: '安装预约支付',
      hideTabBar: true,
      requiresAuth: true
    }
  },
  // 预约安装列表路由
  {
    path: '/installation/bookings',
    name: 'BookingList',
    component: BookingList,
    meta: {
      title: '我的安装预约',
      hideTabBar: true,
      requiresAuth: true
    }
  },
  // 兼容旧路由
  {
    path: '/install-booking',
    redirect: '/installation/booking'
  },
  // 测试页面路由
  {
    path: '/test/wechat',
    name: 'WeChatTest',
    component: WeChatTest,
    meta: {
      title: '微信JSSDK测试',
      hideTabBar: true,
      requiresAuth: false
    }
  },
  {
    path: '/test/branch-wechat',
    name: 'BranchWechatTest',
    component: BranchWechatTest,
    meta: {
      title: '分支机构微信配置测试',
      hideTabBar: true,
      requiresAuth: false
    }
  },
  {
    path: '/test/wechat-menu',
    name: 'WechatMenuTest',
    component: WechatMenuTest,
    meta: {
      title: '微信菜单测试',
      hideTabBar: true,
      requiresAuth: false
    }
  },
  {
    path: '/test/nav',
    name: 'NavTest',
    component: () => import('../views/test/NavTest.vue'),
    meta: {
      title: '导航配置测试',
      hideTabBar: true,
      requiresAuth: false
    }
  },
  {
    path: '/test/token',
    name: 'TokenTest',
    component: () => import('../views/debug/TokenTest.vue'),
    meta: {
      title: 'Token测试',
      hideTabBar: true,
      requiresAuth: false
    }
  },
  {
    path: '/debug/vip-upgrade',
    name: 'VipUpgradeDebug',
    component: () => import('../views/debug/VipUpgradeDebug.vue'),
    meta: {
      title: 'VIP升级调试',
      hideTabBar: true,
      requiresAuth: false
    }
  },
  // 邀请函相关路由
  ...invitationRoutes,
  {
    path: '/vip/list',
    name: 'VipList',
    component: VipList,
    meta: {
      title: 'VIP会员列表',
      requiresAuth: true,
      roles: ['admin'],
      hideTabBar: true
    }
  },
  {
    path: '/vip/detail/:id',
    name: 'VipDetail',
    component: VipDetail,
    meta: {
      title: 'VIP会员详情',
      requiresAuth: true,
      roles: ['admin'],
      hideTabBar: true
    }
  },
  // VIP分红相关路由
  {
    path: '/vip/dividends',
    name: 'VipDividends',
    component: () => import('@/views/vip/VipDividends.vue'),
    meta: {
      title: 'VIP分红',
      requiresAuth: true
    }
  },
  {
    path: '/vip/dividend-history',
    name: 'DividendHistory',
    component: () => import('@/views/vip/DividendHistory.vue'),
    meta: {
      title: '分红记录',
      requiresAuth: true
    }
  },
  {
    path: '/vip/dividend-ranking',
    name: 'DividendRanking',
    component: () => import('@/views/vip/DividendRanking.vue'),
    meta: {
      title: '分红排行',
      requiresAuth: true
    }
  },
  // 404路由
  {
    path: '/:pathMatch(.*)*',
    redirect: '/'
  },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  console.log('🛡️ 路由守卫开始执行:', { from: from.path, to: to.path, query: to.query, hash: window.location.hash })
  
  // 特殊处理：分支机构登录页面跳过复杂的登录状态检查
  if (to.path === '/branch-login' || to.path.startsWith('/branch-login')) {
    console.log('🏢 访问分支机构登录页面，跳过复杂的登录状态检查')
    next()
    return
  }

  // 检查URL中是否有token参数（特别是业务员页面）
  if (to.path === '/salesman' && to.query.token) {
    const token = to.query.token;
    console.log('路由守卫检测到业务员页面token:', token);
    
    // 设置token到localStorage（确保两个key都设置）
    localStorage.setItem('token', token);
    localStorage.setItem('jwt_token', token);
    localStorage.setItem('isLoggedIn', 'true');
    
    // 设置基本用户信息，标记为业务员
    const userInfo = {
      id: parseInt(token),
      is_salesman: 1,
      is_vip: 0,
      is_admin: 0,
      is_engineer: 0,
      is_water_purifier_user: 0,
      is_water_purifier_agent: 0,
      is_pay_institution: 0,
      is_pay_merchant: 0,
      roles: ['业务员']
    };
    
    localStorage.setItem('userInfo', JSON.stringify(userInfo));
    
    // 更新用户存储状态
    const userStore = useUserStore();
    userStore.setToken(token);
    userStore.setUserInfo(userInfo);
    
    console.log('路由守卫已设置业务员登录状态，用户ID:', token);
    
    // 直接允许访问，不进行后续检查
    next();
    return;
  }
  
  // 获取用户信息
  const userStore = useUserStore()
  const isLoggedIn = userStore.isLoggedIn
  const userInfo = userStore.userInfo

  // 检查是否有模拟登录token
  // 在hash路由模式下，参数在hash部分
  const hashParts = window.location.hash.split('?')
  const queryString = hashParts.length > 1 ? hashParts[1] : ''
  const urlParams = new URLSearchParams(queryString)
  const simulateToken = urlParams.get('simulate_token')
  
  if (simulateToken) {
    try {
      // 验证模拟登录token
      const { verifySimulateLogin } = await import('@/api/simulateLogin')
      const response = await verifySimulateLogin(simulateToken)
      
      if (response.code === 0 || response.code === 200) {
        // 设置模拟登录模式
        userStore.setSimulateMode(simulateToken, response.data.user_info)
        
        // 清除URL中的模拟登录参数
        const newUrl = window.location.pathname + window.location.hash.split('?')[0]
        window.history.replaceState({}, '', newUrl)
        
        console.log('模拟登录成功:', response.data.user_info.name || response.data.user_info.phone)
      }
    } catch (error) {
      console.error('模拟登录失败:', error)
    }
  } else {
    // 检查是否已经处于模拟登录模式
    userStore.checkSimulateMode()
  }

  // 获取页面标题
  const title = to.meta.title || '点点够商城'
  document.title = title

  // 检查是否有分支机构代码参数（仅在根路径检查，避免无限循环）
  if (to.path === '/' && to.query.branch_code) {
    const branchCode = to.query.branch_code
    
    // 检查用户是否已登录（使用localStorage作为更可靠的检查）
    const hasToken = localStorage.getItem('token') || localStorage.getItem('branch_token')
    const hasUserInfo = localStorage.getItem('userInfo') || localStorage.getItem('branch_user_info')
    const isUserLoggedIn = hasToken && hasUserInfo
    
    if (!isUserLoggedIn) {
      console.log('🏢 检测到分支机构代码:', branchCode)
      console.log('🔄 跳转到分支机构登录页面')
      
      // 跳转到分支机构登录页面，保持查询参数
      next({
        path: '/branch-login',
        query: { branch_code: branchCode }
      })
      return
    }
  }

  // 特殊处理微信回调页面、登录结果页面、绑定手机号页面和分支机构相关页面
  if (to.path === '/wechat-callback' || to.path === '/wechat-success' || to.path === '/wechat-error' || to.path === '/bind-phone' || 
      to.path === '/branch/wechat-success' || to.path === '/branch-login') {
    next()
    return
  }
  
  // 分支机构页面特殊处理
  if (to.path.startsWith('/branch/')) {
    // 检查分支机构登录状态
    const branchUserId = localStorage.getItem('user_id')
    const branchCode = localStorage.getItem('branch_code')
    const isBranch = localStorage.getItem('isBranch')
    const branchUserInfo = localStorage.getItem('branch_userInfo')
    
    const isBranchLoggedIn = branchUserId && branchCode && isBranch === '1' && branchUserInfo
    
    console.log('🏢 分支机构页面路由守卫检查:', {
      path: to.path,
      branchUserId,
      branchCode,
      isBranch,
      hasBranchUserInfo: !!branchUserInfo,
      isBranchLoggedIn
    })
    
    if (!isBranchLoggedIn) {
      // 分支机构用户未登录，跳转到分支机构登录页面
      const targetBranchCode = branchCode || 'XM0001'
      console.log('🏢 分支机构用户未登录，跳转到分支机构登录页面:', targetBranchCode)
      next({
        path: '/branch-login',
        query: { branch_code: targetBranchCode, redirect: to.fullPath }
      })
      return
    }
    
    // 分支机构用户已登录，允许访问
    next()
    return
  }

  // 检查并恢复登录状态
  const isSimulateMode = sessionStorage.getItem('simulate_mode') === 'true'
  
  if (!isSimulateMode) {
    try {
      const authModule = await import('@/utils/auth')
      
      // 检查认证状态
      const hasAuthData = authModule.isLoggedIn()
      console.log('🛡️ 路由守卫检查:', {
        route: to.path,
        userStoreLoggedIn: userStore.isLoggedIn,
        authDataExists: hasAuthData
      })
      
      // 如果用户存储状态不一致，尝试恢复
      if (!userStore.isLoggedIn && hasAuthData) {
        console.log('🔄 检测到状态不一致，开始恢复...')
        const restored = authModule.restoreAuthState()
        if (restored) {
          console.log('✅ 路由守卫已恢复登录状态')
        } else {
          console.log('❌ 路由守卫恢复登录状态失败')
        }
      }
    } catch (error) {
      console.error('❌ 路由守卫恢复登录状态失败:', error)
    }
  }
  if (isLoggedIn && (!userInfo || Object.keys(userInfo).length === 0) && !isSimulateMode) {
    // 检查是否是从微信回调页面跳转过来的
    const isFromWechatCallback = from.path === '/wechat-callback' ||
                                localStorage.getItem('fromWechatCallback') === 'true';

    if (isFromWechatCallback) {
      try {
        // 尝试从localStorage获取用户信息
        const storedUserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');

        if (storedUserInfo && Object.keys(storedUserInfo).length > 0) {
          userStore.setUserInfo(storedUserInfo);

          // 设置标记，避免重复处理
          localStorage.removeItem('fromWechatCallback');

          // 继续导航
          next();
          return;
        }
      } catch (e) {
        // 静默处理解析错误
      }
    }

    // 清除登录状态并重定向到登录页面
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
    localStorage.removeItem('isLoggedIn')
    localStorage.removeItem('wechat_info')
    localStorage.removeItem('tempUserInfo')
    localStorage.removeItem('needBindPhone')
    userStore.clearUserInfo()

    // 检查是否是分支机构用户
    const branchCode = localStorage.getItem('branch_code')
    const isBranchPage = to.path.startsWith('/branch/')
    
    if (isBranchPage || branchCode) {
      // 分支机构用户跳转到分支机构登录页面
      const targetBranchCode = branchCode || 'XM0001'
      console.log('🏢 分支机构用户登录状态异常，跳转到分支机构登录页面:', targetBranchCode)
      next({
        path: '/branch-login',
        query: { branch_code: targetBranchCode }
      })
      return
    }

    const host = window.location.host
    const isProduction = host.includes('pay.itapgo.com')

    if (isProduction) {
      // 生产环境
      window.location.href = `/app/#/login`
      return
    } else {
      // 开发环境
      next('/login')
      return
    }
  }

  // 如果页面需要登录而用户未登录，则跳转到登录页
  // 但在模拟登录模式下允许访问
  if (to.meta.requiresAuth && !isLoggedIn && !isSimulateMode) {
    // 检查是否是分支机构页面
    const isBranchPage = to.path.startsWith('/branch/')
    const branchCode = localStorage.getItem('branch_code')
    
    if (isBranchPage || branchCode) {
      // 分支机构用户跳转到分支机构登录页面
      const targetBranchCode = branchCode || 'XM0001'
      console.log('🏢 分支机构用户未登录，跳转到分支机构登录页面:', targetBranchCode)
      next({
        path: '/branch-login',
        query: { branch_code: targetBranchCode, redirect: to.fullPath }
      })
      return
    }
    
    // 记录当前环境和主机名，以便确定正确的登录页面URL
    const host = window.location.host
    const isProduction = host.includes('pay.itapgo.com')

    if (isProduction) {
      // 生产环境下使用全路径重定向
      // 防止路由导航，使用硬重定向
      window.location.href = `/app/#/login?redirect=${encodeURIComponent(to.fullPath)}`
      return
    } else {
      // 开发环境下使用router正常导航
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }
  }

  // 如果用户已登录并试图访问登录页，直接跳转到个人中心
  // 但在模拟登录模式下允许访问登录页
  if (isLoggedIn && to.path === '/login' && !isSimulateMode) {
    next('/user')
    return
  }

  // 检查用户是否已登录但未绑定手机号
  // 但在模拟登录模式下跳过此检查
  if (isLoggedIn && userInfo && to.path !== '/bind-phone' && !isSimulateMode) {
    // 检查是否是分支机构用户（分支机构用户无需绑定手机号）
    const wechatInfo = JSON.parse(localStorage.getItem('wechat_info') || '{}')
    const isBranchUser = wechatInfo.branch_id || userInfo.branch_id
    
    // 判断用户是否有手机号
    const hasPhone = userInfo.phone && userInfo.phone.length > 0

    // 如果登录成功但没有手机号，且不是分支机构用户，强制跳转到绑定手机号页面
    if (!hasPhone && !isBranchUser) {
      // 检查临时用户信息中是否有必要的微信数据
      const tempUserInfo = JSON.parse(localStorage.getItem('tempUserInfo') || '{}')
      const hasRequiredData = tempUserInfo.openid ||
        (tempUserInfo.wechat_nickname || tempUserInfo.nickname) ||
        (tempUserInfo.wechat_avatar || tempUserInfo.avatar)

      if (!hasRequiredData) {
        // 记录当前环境和主机名，以便确定正确的登录页面URL
        const host = window.location.host
        const isProduction = host.includes('pay.itapgo.com')

        // 清除过时的登录状态，防止循环跳转
        localStorage.removeItem('token')
        localStorage.removeItem('userInfo')
        localStorage.removeItem('tempUserInfo')
        localStorage.removeItem('needBindPhone')
        userStore.clearUserInfo()

        // 检查是否是分支机构用户
        const branchCode = localStorage.getItem('branch_code')
        const isBranchPage = to.path.startsWith('/branch/')
        
        if (isBranchPage || branchCode) {
          // 分支机构用户跳转到分支机构登录页面
          const targetBranchCode = branchCode || 'XM0001'
          console.log('🏢 分支机构用户缺少必要数据，跳转到分支机构登录页面:', targetBranchCode)
          next({
            path: '/branch-login',
            query: { branch_code: targetBranchCode }
          })
          return
        }

        if (isProduction) {
          // 生产环境下使用全路径重定向
          window.location.href = `/app/#/login`
          return
        } else {
          // 开发环境下使用router正常导航
          next('/login')
          return
        }
      }

      // 设置needBindPhone标志
      localStorage.setItem('needBindPhone', 'true')

      // 记录当前环境和主机名，以便确定正确的绑定页面URL
      const host = window.location.host
      const isProduction = host.includes('pay.itapgo.com')

      if (isProduction) {
        // 生产环境下使用全路径重定向
        window.location.href = `/app/#/bind-phone`
        return
      } else {
        // 开发环境下使用router正常导航
        next('/bind-phone')
        return
      }
    }
  }

  // 角色路由权限控制
  if (to.meta.roles && to.meta.roles.length > 0) {
    // 首先检查用户是否登录
    if (!isLoggedIn && !isSimulateMode) {
      // 未登录用户访问需要角色的页面，重定向到登录页
      console.log('未登录用户访问需要角色的页面，重定向到登录页')
      
      // 检查是否是分支机构页面
      const isBranchPage = to.path.startsWith('/branch/')
      const branchCode = localStorage.getItem('branch_code')
      
      if (isBranchPage || branchCode) {
        // 分支机构用户跳转到分支机构登录页面
        const targetBranchCode = branchCode || 'XM0001'
        console.log('🏢 分支机构用户访问需要角色的页面但未登录，跳转到分支机构登录页面:', targetBranchCode)
        next({
          path: '/branch-login',
          query: { branch_code: targetBranchCode, redirect: to.fullPath }
        })
        return
      }
      
      const host = window.location.host
      const isProduction = host.includes('pay.itapgo.com')

      if (isProduction) {
        // 生产环境下使用全路径重定向
        window.location.href = `/app/#/login?redirect=${encodeURIComponent(to.fullPath)}`
        return
      } else {
        // 开发环境下使用router正常导航
        next({
          path: '/login',
          query: { redirect: to.fullPath }
        })
        return
      }
    }

    // 用户已登录，检查角色权限
    if ((isLoggedIn && userInfo) || isSimulateMode) {
      // 检查用户是否拥有所需角色
      const requiredRoles = to.meta.roles
      const hasRequiredRole = requiredRoles.some(role => {
        switch (role) {
          case 'vip':
            return userInfo.is_vip === 1 || userInfo.is_vip === '1'
          case 'admin':
            return userInfo.is_admin === 1 || userInfo.is_admin === '1'
          case 'salesman':
            return userInfo.is_salesman === 1 || userInfo.is_salesman === '1'
          case 'engineer':
            return userInfo.is_engineer === 1 || userInfo.is_engineer === '1'
          case 'water_purifier_user':
            return userInfo.is_water_purifier_user === 1 || userInfo.is_water_purifier_user === '1'
          case 'water_purifier_agent':
            return userInfo.is_water_purifier_agent === 1 || userInfo.is_water_purifier_agent === '1'
          case 'pay_institution':
            return userInfo.is_pay_institution === 1 || userInfo.is_pay_institution === '1'
          case 'pay_merchant':
            return userInfo.is_pay_merchant === 1 || userInfo.is_pay_merchant === '1'
          default:
            return false
        }
      })

      if (hasRequiredRole || isSimulateMode) {
        next()
      } else {
        // 用户没有所需角色，跳转到首页并提示
        console.warn(`用户缺少访问权限，需要角色: ${requiredRoles.join(', ')}`)
        next('/')
        return
      }
    } else {
      // 登录状态异常，重定向到登录页
      console.log('登录状态异常，重定向到登录页')
      const host = window.location.host
      const isProduction = host.includes('pay.itapgo.com')

      if (isProduction) {
        // 生产环境下使用全路径重定向
        window.location.href = `/app/#/login?redirect=${encodeURIComponent(to.fullPath)}`
        return
      } else {
        // 开发环境下使用router正常导航
        next('/login')
        return
      }
    }
  } else {
    // 不需要角色控制的页面
    next()
  }
})

// 处理导航错误
router.onError((error) => {
  // 生产环境关闭日志
})

// 阻止NavigationDuplicated错误
const originalPush = router.push
router.push = function push(location) {
  return originalPush.call(this, location).catch(err => {
    if (err.name !== 'NavigationDuplicated') {
      // 生产环境关闭日志
      return Promise.reject(err)
    }
    return Promise.resolve()
  })
}

export default router
